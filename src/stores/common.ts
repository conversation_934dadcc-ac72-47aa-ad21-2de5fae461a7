import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { getCityCodeApi, getDataTreeList, getDetailData, searchAttributeList } from '@/api/common'
import { handleTree } from '@/utils/format'
import type { AllGeoJSON } from '@turf/turf'

export const useCommonStore = defineStore('common', () => {
  const mapLevel = ref('')

  const dictList = ref<any[]>([])
  const treeList = ref<any[]>([])
  const rightTreeList = ref<any[]>([])

  const currentId = ref('')

  const searchList = ref<any[]>([])
  const searchParams = ref<any>({})

  const citys = ref([])
  const currentLocation = ref<any[]>([])
  const currentCitys = ref<any[]>([])

  const isSearch = ref<boolean>(false)

  const geometryWkt = ref<any>(null)

  // 当前选中的树节点信息
  const currentTreeNode = ref<any>(null)

  // 分页相关
  const currentPage = ref(1)
  const pageSize = ref(10)
  const isLoading = ref(false)

  const isTable = ref<boolean>(false)
  const attributeList = ref<any[]>([])
  const tableList = ref<any[]>([])
  const tableTotal = ref(0)

  const typeGroup = ref('')

  // 模糊搜索
  const keyword = ref('')

  const downloadId = ref('')

  const selectGeometry = ref<AllGeoJSON>({
    type: 'FeatureCollection',
    features: [],
  })
  const selectId = ref('')

  const isShow = ref(false)

  const isDetail = ref(false)
  const attributeListDetail = ref<any[]>([])

  // 模糊查询动态参数
  const vagueLabel = computed(() => {
    const list = dictList.value.filter((item: any) => item.isVagueCondition === 1)
    return list.map((item: any) => item.attributeLabel).join(',')
  })

  async function getTreeAction() {
    const res = await getDataTreeList()
    dictList.value = res.data
    const treeData = handleTree(res.data, 'attributeId', 'attributePid')

    rightTreeList.value = pruneTreeBottomUpAction(treeData)
    treeList.value = pruneTreeBottomUpAction(treeData)

    currentId.value = findLeafNodes(treeList.value[0])

    getSearchListAction(currentId.value)
    typeGroup.value = findPathToRootIterativeAction(currentId.value, dictList.value).join(',')
  }

  // 获取搜索条件
  async function getSearchListAction(id: string) {
    const searchRes = await searchAttributeList({ attributeId: id })
    searchList.value = searchRes.rows!
    searchList.value.forEach((item: any) => {
      searchParams.value[item.prop] = item.defaultValue
    })
  }

  function pruneTreeBottomUpAction(nodes: any[]) {
    // 如果是空数组或无效输入，直接返回
    if (!nodes || nodes.length === 0) {
      return []
    }

    // 使用 map 创建一个新数组，实现非破坏性操作
    return nodes.map((node) => {
      // 1. 创建当前节点的新副本
      const newNode = { ...node } // 使用扩展语法复制属性

      // 2. **先处理当前节点**
      if (newNode.children && newNode.children.length > 0) {
        const allChildrenAreLeaves = newNode.children.every(
          (child: any) => !child.children || child.children.length === 0,
        )

        // 如果所有子节点都是空的叶子节点，则清空当前节点的 children
        if (allChildrenAreLeaves) {
          newNode.children = []
        }
      }

      // 2. **在递归处理子节点**
      if (newNode.children && newNode.children.length > 0) {
        newNode.children = pruneTreeBottomUpAction(newNode.children)
      }

      return newNode
    })
  }

  // 查找tree的最子级的id
  function findLeafNodes(tree: any) {
    const id = tree.attributeId
    if (tree.children && tree.children.length > 0) {
      return findLeafNodes(tree.children[0])
    } else {
      return id
    }
  }

  // 获取区划
  async function getCityCodeAction() {
    const res = await getCityCodeApi()
    console.log(res)
    citys.value = res.data
    currentLocation.value = res.data.filter((item: any) => item.name === '浙江省')
    console.log(currentLocation.value)
    currentCitys.value = res.data.filter((item: any) => {
      return item.parentId == currentLocation.value[0].id
    })
  }

  async function getCurrentCitysAction(id: number) {
    currentCitys.value = citys.value.filter((item: any) => {
      return item.parentId == id
    })
  }

  async function getDetailDataAction() {
    attributeListDetail.value = []
    isSearch.value = true
    isDetail.value = true
    const res = await getDetailData(selectId.value)
    const validProperties = JSON.parse(res.data.validProperties)

    for (const key in res.data.attribute) {
      attributeListDetail.value.push({
        label: res.data.attribute[key],
        value: validProperties[key],
      })
    }
    return res
  }

  // 找到从当前节点到根节点的路径
  function findPathToRootIterativeAction(id: string, treeDataList: any[]) {
    const path = []
    let currentId = id

    // 使用 Map 优化查找性能，避免每次循环都遍历整个数组
    // 如果 treeDataList 很大，这个优化非常重要
    const nodeMap = new Map(treeDataList.map((item) => [item.attributeId, item]))

    while (currentId && currentId !== '0') {
      const current = nodeMap.get(currentId)

      path.unshift(current.attributeId)

      currentId = current.attributePid
    }

    return path
  }

  return {
    mapLevel,
    dictList,
    rightTreeList,
    treeList,
    currentId,
    currentLocation,
    currentCitys,
    isSearch,
    geometryWkt,
    isTable,
    currentTreeNode,
    currentPage,
    pageSize,
    isLoading,
    attributeList,
    tableList,
    tableTotal,
    selectGeometry,
    selectId,
    isDetail,
    isShow,
    attributeListDetail,
    searchList,
    searchParams,
    typeGroup,
    keyword,
    downloadId,
    vagueLabel,
    getTreeAction,
    getCityCodeAction,
    getCurrentCitysAction,
    getDetailDataAction,
    getSearchListAction,
    findPathToRootIterativeAction,
    pruneTreeBottomUpAction,
  }
})

<template>
  <div class="right">
    <div class="kanban" v-if="!isDetail">
      <div class="header">
        <img :src="leftArrow" alt="" />
        <span>查询看板</span>
        <img :src="rightArrow" alt="" />
      </div>
      <div class="close" @click="closeHandler">
        <el-icon style="cursor: pointer"><Close /></el-icon>
      </div>
      <div class="kanban-box">
        <div class="operate">
          <div class="img-item" @click="mapDrawHandler">
            <i class="icon draw"></i>
            <p>图上绘制</p>
          </div>
          <div class="img-item" @click="uploadHandler">
            <i class="icon uoload"></i>
            <p>图形上传</p>
          </div>
          <div class="img-item" @click="isShow = !isShow">
            <i class="icon positioning"></i>
            <p>区划定位</p>
          </div>
        </div>
        <input
          ref="shpFileRef"
          type="file"
          accept=".zip,.ZIP,.rar,.RAR"
          style="display: none"
          @change="fileChangeHandler"
        />
        <div class="search">
          <div class="search-item search-select">
            <el-tree-select
              v-model="currentId"
              :data="rightTreeList"
              :props="{
                children: 'children',
                label: 'attributeName',
                value: 'attributeId',
              }"
              value-key="attributeId"
              placeholder="请选择类型"
              @change="changeHandler"
            ></el-tree-select>
          </div>

          <template v-for="search in searchList" :key="search.id">
            <div class="search-item search-input" v-if="search.type === 'input'">
              <el-input
                v-model="searchParams[search.prop]"
                :placeholder="search.placeholder || `请输入${search.label}`"
                clearable
              ></el-input>
            </div>
            <div class="search-item search-select" v-if="search.type === 'select'">
              <el-select
                v-model="searchParams[search.prop]"
                :placeholder="search.placeholder || `请选择${search.label}`"
                style="width: 100%"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div class="search-item search-date" v-if="search.type === 'date-range'">
              <el-date-picker
                v-model="searchParams[search.prop]"
                type="daterange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                clearable
              />
            </div>
          </template>
        </div>
        <div
          class="btn"
          :style="{
            background: `url(${btnBg}) no-repeat`,
            'background-size': '100% 100%',
          }"
          @click="searchHandler"
        >
          查询
        </div>
      </div>
    </div>
    <div class="detail" v-else>
      <div class="nav-box">
        <div class="nav-title">成果详情</div>
        <div class="nav-return" @click="isDetail = false">
          <img :src="returnImg" alt="" />
        </div>
      </div>
      <div class="detail-box">
        <div class="detail-title">
          <img :src="highlights" width="15" height="15" alt="" />
          <span>数据信息</span>
        </div>
        <div
          class="detail-content"
          :style="{
            background: `url(${detailContent}) no-repeat`,
            'background-size': '100% 100%',
          }"
        >
          <template v-for="item in attributeListDetail" :key="item.attributeId">
            <div class="detail-data">
              <div class="attr">{{ item.label }}：</div>
              <div class="data">{{ item.value }}</div>
            </div>
          </template>
          <div
            class="btn"
            :style="{
              background: `url(${btnBg}) no-repeat`,
              'background-size': '100% 100%',
            }"
            @click="downloadHandler"
          >
            下载
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { leftArrow, rightArrow, btnBg, returnImg, highlights, detailContent } from '@/utils/base64'
import { Close } from '@element-plus/icons-vue'
import { useEventBus } from '@vueuse/core'
import { useCommonStore } from '@/stores/common'
import { storeToRefs } from 'pinia'
import {
  dataSearch,
  downloadGeojson,
  fileConvertWkt,
  getAttributeDetail,
  getDataTreeList,
} from '@/api/common'
import { handleTree } from '@/utils/format'

const commonStore = useCommonStore()
const {
  dictList,
  treeList,
  rightTreeList,
  currentLocation,
  isSearch,
  geometryWkt,
  isDetail,
  attributeListDetail,
  isShow,
  currentId,
  searchList,
  searchParams,
  typeGroup,
  attributeList,
  tableList,
  tableTotal,
  isTable,
  currentTreeNode,
  downloadId,
} = storeToRefs(commonStore)

const drawBus = useEventBus('draw')
const uploadBus = useEventBus('upload')
const searchBus = useEventBus('search')

const options = [
  {
    value: '2米以上',
    label: '2米以上',
  },
  {
    value: '2米',
    label: '2米',
  },
  {
    value: '优于1米',
    label: '优于1米',
  },
  {
    value: '0.2米',
    label: '0.2米',
  },
]

const shpFileRef = ref<HTMLInputElement>()

function mapDrawHandler() {
  drawBus.emit()
}

function uploadHandler() {
  shpFileRef.value?.click()
}

// 文件上传
async function fileChangeHandler(e: Event) {
  const target = e.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    const formdata = new FormData()
    formdata.append('file', file)
    const res = await fileConvertWkt(formdata)
    geometryWkt.value = res?.msg
    uploadBus.emit()
  }
}

// 关闭查询看板
function closeHandler() {
  isSearch.value = false
}

function changeHandler(val: any) {
  commonStore.getSearchListAction(val)
  typeGroup.value = commonStore.findPathToRootIterativeAction(val, dictList.value).join(',')
}

async function searchHandler() {
  const params: any = {}

  // 处理空值 '' 和 null
  for (const key in searchParams.value) {
    const value = searchParams.value[key]
    if (value) {
      params[key] = searchParams.value[key]
    }
  }

  const query = {
    typeGroup: typeGroup.value,
    wktGroup: geometryWkt.value,
    citySign: currentLocation.value[currentLocation.value.length - 1].id,
    params,
  }
  const list = await getAttributeDetail(currentId.value)
  attributeList.value = list.data
  const res = await dataSearch(query)
  const tableData = res.rows?.map((item: any) => {
    return {
      ...item,
      ...JSON.parse(item.validProperties),
    }
  })

  tableList.value = tableData || []
  tableTotal.value = res.total || 0
  isTable.value = true
  currentTreeNode.value = dictList.value.find((item: any) => item.attributeId === currentId.value)
  searchBus.emit(query)

  // 恢复左侧树结构
  const treeRes = await getDataTreeList()
  const treeData = handleTree(treeRes.data, 'attributeId', 'attributePid')
  treeList.value = commonStore.pruneTreeBottomUpAction(treeData)
}

async function downloadHandler() {
  const res = await downloadGeojson({
    id: downloadId.value,
  })
  const url = window.URL.createObjectURL(res as any)
  const link = document.createElement('a')
  link.href = url
  link.download = 'download.zip'
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}
</script>

<style scoped lang="scss">
.right {
  width: 100%;
  position: relative;
  height: calc(100% - 60px);
  background: linear-gradient(180deg, rgba(22, 35, 65, 0.79) 75%, rgba(0, 0, 0, 0.15));
  color: hsla(0, 0%, 100%, 0.9);
  pointer-events: all;
}

.kanban {
  width: 100%;
  height: 100%;
  /* position: relative; */

  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    gap: 10px;
    line-height: 44px;
    background: url(../../../assets/img/search-bg.png) no-repeat;
    background-size: 100% 100%;
  }
  .close {
    position: absolute;
    left: 10px;
    top: 1.2%;
    font-size: 24px;
    z-index: 99;
    cursor: pointer;
  }
  .kanban-box {
    .operate {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 10px 24px;
      .img-item {
        margin-top: 20px;
        text-align: center;
        font-size: 20px;
        cursor: pointer;
        .icon {
          padding: 15px;
          background: hsla(0, 0%, 100%, 0.886);
          color: #c17745;
          border-radius: 50%;
          box-sizing: border-box;
          font-weight: 700;
          font-size: 28px;

          font-family: 'iconfont';
        }
        .draw::before {
          content: '\e733';
          color: #c17745;
        }
        .uoload::before {
          content: '\e658';
          color: #285feb;
        }
        .positioning::before {
          content: '\e9b3';
          color: #5b9a1d;
        }
        p {
          margin-top: 28px;
        }
      }
    }
    .search {
      margin-top: 15px;
      padding: 0 15px;
      .search-item {
        margin-top: 15px;
      }
      .search-select {
        :deep(.el-select__wrapper) {
          border: 1px solid rgba(102, 127, 178, 0.5);
          border-radius: 4px;
          box-shadow: none;
          background: none;
        }
      }
      .search-date {
        display: flex;
      }
    }
  }
}
.btn {
  margin: 20px 15px;
  float: right;
  padding: 10px 30px;
  font-size: 20px;
  cursor: pointer;
}
.detail {
  width: 100%;
  height: 100%;
  .nav-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: url(../../../assets/img/title.png) no-repeat;
    background-size: 100% 100%;
    width: 98%;
    line-height: 38px;
    margin: -10px 0 0 0;
    padding-left: 20px;
    color: #fff;
    .nav-title {
      font-family: FZDHTJW;
      font-weight: 700;
      font-size: 24px;
      padding-left: 28px;
    }
    .nav-return {
      margin-top: 14px;
      padding-right: 28px;
      cursor: pointer;
    }
  }
  .detail-box {
    padding: 10px;
    height: calc(100% - 40px);
    .detail-title {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 18px;
      font-weight: 700;
      margin-bottom: 10px;
    }
    .detail-content {
      padding: 20px;
      height: calc(100% - 30px);
      .detail-data {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        font-size: 18px;
        .attr {
          white-space: nowrap;
          width: 25%;
        }
        .data {
          max-width: 75%;
        }
      }
    }
  }
}
</style>
